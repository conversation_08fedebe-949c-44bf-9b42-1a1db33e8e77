"""
Example sentences to test spaCy and its language models.

>>> from spacy.lang.hsb.examples import sentences
>>> docs = nlp.pipe(sentences)
"""


sentences = [
    "To bě<PERSON><PERSON> wjelgin raźone a jo se wót luźi derje pśiwzeło. Tak som dožywiła wjelgin",
    "<PERSON><PERSON> pśewóźowarce stej groniłej, až how w serbskich stronach njama Santa Claus nic pytaś.",
    "A ten sobuźěłaśeŕ Statneje biblioteki w Barlinju jo pśimjeł drogotne knigły bźez rukajcowu z nagima rukoma!",
    "Take wobchadanje z našym kulturnym derbstwom zewšym njejźo.",
    "Wopśimjeśe drugich pśinoskow jo było na wusokem niwowje, ako pśecej.",
]
