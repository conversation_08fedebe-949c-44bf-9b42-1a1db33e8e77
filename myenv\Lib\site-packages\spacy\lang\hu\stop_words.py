STOP_WORDS = set(
    """
a abban ahhoz ahogy ahol aki akik akkor akár alatt amely amelyek amelyekben
amelyeket amelyet amelynek ami amikor amit amolyan amíg annak arra arról az
azok azon azonban azt aztán azután azzal azért

be belül benne bár

cikk cikkek cikkeket csak

de

e ebben eddig egy egyes egyetlen egyik egyre egyéb egész ehhez ekkor el ellen
elo eloször elott elso elég előtt emilyen ennek erre ez ezek ezen ezt ezzel
ezért

fel felé

ha hanem hiszen hogy hogyan hát

ide igen ill ill. illetve ilyen ilyenkor inkább is ismét ison itt

jobban jó jól

kell kellett keressünk keresztül ki kívül között közül

le legalább legyen lehet lehetett lenne lenni lesz lett

ma maga magát majd meg mellett mely melyek mert mi miatt mikor milyen minden
mindenki mindent mindig mint mintha mit mivel miért mondta most már más másik
még míg

nagy nagyobb nagyon ne nekem neki nem nincs néha néhány nélkül

o oda ok oket olyan ott

pedig persze például

rá

s saját sem semmi sok sokat sokkal stb. szemben szerint szinte számára szét

talán te tehát teljes ti tovább továbbá több túl ugyanis

utolsó után utána

vagy vagyis vagyok valaki valami valamint való van vannak vele vissza viszont
volna volt voltak voltam voltunk

által általában át

én éppen és

így

ön össze

úgy új újabb újra

ő őket
""".split()
)
