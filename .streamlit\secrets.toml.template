# Streamlit Secrets Template
# Copy this file to secrets.toml and fill in your actual API keys
# DO NOT commit secrets.toml to version control
#
# NOTE: OpenAI API key is now required and no longer shown in the interface
# The app will read the key from secrets only for better security

# OpenAI API Configuration
[openai]
api_key = "your-openai-api-key-here"

# Eden AI Configuration (Optional - for enhanced AI detection)
[eden_ai]
api_key = "your-eden-ai-api-key-here"
providers = "sapling,writer,originalityai"

# Application Configuration
[app]
debug_mode = false
log_level = "INFO"
max_concurrent_users = 10

# Performance Settings
[performance]
cache_ttl = 3600
max_video_fps = 10
enable_gpu = false

# Security Settings
[security]
enable_rate_limiting = true
max_requests_per_minute = 60
enable_audit_logging = true

# File Storage (for deployment)
[storage]
reports_retention_days = 30
max_file_size_mb = 50
enable_cleanup = true
