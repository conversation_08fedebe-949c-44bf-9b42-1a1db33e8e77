# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
myenv/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Streamlit specific
.streamlit/secrets.toml
.streamlit/credentials.toml

# Application specific - AI Interview System
interview_reports/
session_data/
*.mp3
*.wav
*.log
yolov8n.pt
speech_*.mp3
temp_audio_*
interview_system.log

# AI Model files
*.pt
*.pth
*.onnx
*.pb
*.h5
*.pkl
*.pickle
models/
checkpoints/

# Generated reports and data
reports/
exports/
backups/
*.csv
*.xlsx
*.json
!requirements.txt
!package.json
!.streamlit/config.toml
!.streamlit/secrets.toml.template

# IDE specific
.vscode/
.idea/
*.swp
*.swo
*~

# OS specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Model files (too large for git)
*.pt
*.pth
*.onnx
*.pb

# Data files
*.csv
*.json
*.xlsx
*.pickle
*.pkl

# Audio/Video files
*.mp4
*.avi
*.mov
*.mkv
*.flv
*.wmv

# Image files (except small ones)
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.svg

# Backup files
*.bak
*.backup
*.old

# Cache directories
.cache/
cache/
__pycache__/

# Local configuration
config_local.py
settings_local.py

# API keys and secrets
.env.local
.env.production
secrets.json
api_keys.txt
openai_key.txt
eden_ai_key.txt
.secrets/
credentials/

# Interview System specific secrets
interview_config.json
user_data/
private_keys/

# Test files
test_*.py
*_test.py
tests/

# Documentation build
docs/build/
docs/_build/

# Virtual environment
venv*/
env*/
ENV*/

# Package files
*.tar.gz
*.zip
*.rar
*.7z

# Database files
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log.*
error.log
debug.log
access.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache (https://parceljs.org/)
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/
